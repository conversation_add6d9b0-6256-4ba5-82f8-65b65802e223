/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Source <PERSON> Sans CN', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.5;
    color: #333333;
    background-color: #ffffff;
}

.container {
    max-width: 1920px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 顶部导航栏 */
.header {
    background-color: #ffffff;
    height: 88px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1000;
}

.nav-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    max-width: 1220px;
    margin: 0 auto;
}

.logo-section {
    flex-shrink: 0;
}

.logo {
    height: 40px;
    width: auto;
}

.nav-menu {
    display: flex;
    gap: 40px;
    margin-left: 60px;
}

.nav-item {
    text-decoration: none;
    color: #333333;
    font-size: 16px;
    font-weight: 400;
    padding: 10px 0;
    position: relative;
    transition: color 0.3s ease;
}

.nav-item.active {
    color: #2A6AFF;
}

.nav-item.active::after {
    content: '';
    position: absolute;
    bottom: -20px;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(270deg, #2A6AFF 0%, #5C96FF 100%);
    border-radius: 2px;
}

.nav-item:hover {
    color: #2A6AFF;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.control-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    color: #333333;
    font-size: 14px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.control-btn:hover {
    background-color: #f5f5f5;
}

.control-btn img {
    width: 18px;
    height: 16px;
}

.login-btn {
    background: linear-gradient(270deg, #2A6AFF 0%, #5C96FF 100%);
    color: #ffffff;
    border: none;
    padding: 10px 24px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.login-btn:hover {
    opacity: 0.9;
}

/* 主横幅区域 */
.hero-banner {
    background: linear-gradient(135deg, #E9EEFD 0%, #F0F4FF 100%);
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.banner-content {
    max-width: 1220px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.banner-text {
    flex: 1;
    max-width: 600px;
}

.banner-text h1 {
    font-size: 52px;
    font-weight: 400;
    line-height: 1.5;
    color: #333333;
    margin-bottom: 40px;
}

.cta-button {
    background: linear-gradient(135deg, #FF6B35 0%, #FF8A50 100%);
    color: #ffffff;
    border: none;
    padding: 16px 32px;
    border-radius: 6px;
    font-size: 18px;
    font-weight: 500;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 2px solid #FF6B35;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 107, 53, 0.3);
}

.banner-images {
    position: relative;
    flex-shrink: 0;
    margin-left: 40px;
}

.banner-left,
.banner-right {
    max-width: 300px;
    height: auto;
}

.banner-right {
    margin-left: 20px;
}

/* 核心功能区域 */
.core-features {
    padding: 80px 0;
    background-color: #ffffff;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 60px;
    max-width: 1220px;
    margin: 0 auto;
}

.feature-item {
    text-align: center;
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    border-radius: 12px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.feature-icon.icon1 {
    background-color: #FFE6E6;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FF4444"><path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/></svg>');
}

.feature-icon.icon2 {
    background-color: #E6F3FF;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%232A6AFF"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>');
}

.feature-icon.icon3 {
    background-color: #E6FFE6;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2300AA00"><path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/></svg>');
}

.feature-icon.icon4 {
    background-color: #FFF0E6;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FF8800"><path d="M12 1L3 5v6c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V5l-9-4z"/></svg>');
}

.feature-item h3 {
    font-size: 18px;
    font-weight: 500;
    color: #ffffff;
    background: rgba(51, 51, 51, 0.8);
    padding: 12px 16px;
    border-radius: 8px;
    margin: 0;
}

/* 平台标语 */
.platform-slogan {
    background: linear-gradient(135deg, #2A6AFF 0%, #5C96FF 100%);
    padding: 40px 0;
    text-align: center;
}

.platform-slogan h2 {
    font-size: 36px;
    font-weight: 500;
    color: #ffffff;
    letter-spacing: 2px;
}

/* 产品展示区域 */
.products-section {
    padding: 80px 0;
    background-color: #ffffff;
}

.section-title {
    font-size: 36px;
    font-weight: 500;
    color: #333333;
    text-align: center;
    margin-bottom: 60px;
}

.products-layout {
    display: flex;
    gap: 40px;
    max-width: 1220px;
    margin: 0 auto;
}

.category-section {
    flex-shrink: 0;
    width: 300px;
    background: linear-gradient(135deg, #2A6AFF 0%, #5C96FF 100%);
    border-radius: 10px;
    padding: 30px;
    color: #ffffff;
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.category-header h3 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
}

.view-more {
    color: #ffffff;
    text-decoration: none;
    font-size: 14px;
    opacity: 0.9;
}

.view-more:hover {
    opacity: 1;
}

.category-tags {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.tag-row {
    display: flex;
    gap: 12px;
}

.tag {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 8px 16px;
    font-size: 14px;
    color: #ffffff;
    cursor: pointer;
    transition: background-color 0.3s ease;
    flex: 1;
    text-align: center;
}

.tag:hover {
    background: rgba(255, 255, 255, 0.3);
}

.products-grid {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 20px;
}

.product-card {
    background: #F5F5F5;
    border-radius: 10px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.product-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
    background-color: #e0e0e0;
}

.product-card h4 {
    padding: 12px;
    font-size: 14px;
    font-weight: 400;
    color: #ffffff;
    background: rgba(51, 51, 51, 0.8);
    margin: 0;
    text-align: center;
}

.view-more-section {
    text-align: center;
    margin-top: 40px;
}

.view-more-link {
    color: #ffffff;
    text-decoration: none;
    font-size: 18px;
    font-weight: 500;
    background: rgba(51, 51, 51, 0.8);
    padding: 12px 24px;
    border-radius: 8px;
    transition: background-color 0.3s ease;
}

.view-more-link:hover {
    background: rgba(51, 51, 51, 0.9);
}

/* 标签筛选区域 */
.filter-tags {
    padding: 40px 0;
    background-color: #ffffff;
}

.tags-wrapper {
    display: flex;
    justify-content: center;
    gap: 20px;
    max-width: 1220px;
    margin: 0 auto;
}

.filter-tag {
    padding: 12px 24px;
    border: 1px solid #E4E4E4;
    border-radius: 25px;
    font-size: 16px;
    color: #333333;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #ffffff;
}

.filter-tag.active {
    background: linear-gradient(270deg, #2A6AFF 0%, #5C96FF 100%);
    color: #ffffff;
    border-color: transparent;
}

.filter-tag:hover {
    border-color: #2A6AFF;
    color: #2A6AFF;
}

.filter-tag.active:hover {
    color: #ffffff;
}

/* 案例展示区域 */
.case-study {
    padding: 80px 0;
    background-color: #ffffff;
}

.case-card {
    max-width: 1220px;
    margin: 0 auto;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    display: flex;
    gap: 40px;
    padding: 28px;
}

.case-image {
    flex-shrink: 0;
    width: 588px;
    height: 364px;
    border-radius: 8px;
    overflow: hidden;
}

.case-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.case-content {
    flex: 1;
    padding: 25px 0;
}

.case-content h3 {
    font-size: 24px;
    font-weight: 400;
    color: #333333;
    margin-bottom: 40px;
}

.case-section {
    margin-bottom: 30px;
}

.case-section h4 {
    font-size: 16px;
    font-weight: 400;
    color: #ffffff;
    background: rgba(51, 51, 51, 0.8);
    padding: 8px 12px;
    border-radius: 4px;
    margin-bottom: 12px;
    display: inline-block;
}

.case-section p {
    font-size: 14px;
    line-height: 1.6;
    color: #ffffff;
    background: rgba(51, 51, 51, 0.8);
    padding: 12px;
    border-radius: 4px;
}

/* 合作工厂展示 */
.partner-factories {
    padding: 80px 0;
    background: url('images/main-bg-dc2da7.png') center/cover no-repeat;
    position: relative;
}

.partner-factories::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(233, 238, 253, 0.9) 0%, rgba(240, 244, 255, 0.9) 100%);
}

.partner-factories .container {
    position: relative;
    z-index: 1;
}

.factories-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    max-width: 1220px;
    margin: 0 auto;
}

.factory-card {
    background: #ffffff;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.factory-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.factory-icon {
    width: 48px;
    height: 48px;
    margin: 0 auto 16px;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #DDDDDD;
}

.factory-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.factory-card h3 {
    font-size: 20px;
    font-weight: 400;
    color: #333333;
    margin-bottom: 20px;
    letter-spacing: 2px;
}

.factory-info {
    margin-bottom: 24px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 15px;
    color: #999999;
}

.icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.location-icon {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23999999"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></svg>');
}

.value-icon {
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23999999"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>');
}

.detail-btn {
    background: linear-gradient(270deg, #2A6AFF 0%, #5C96FF 100%);
    color: #ffffff;
    border: none;
    padding: 10px 24px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 400;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.detail-btn:hover {
    opacity: 0.9;
}

/* 底部背景区域 */
.footer-bg {
    background: #081C4D;
    padding: 60px 0 20px;
    color: #ffffff;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    max-width: 1220px;
    margin: 0 auto;
    margin-bottom: 40px;
}

.footer-info h3 {
    font-size: 20px;
    font-weight: 400;
    margin-bottom: 20px;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
}

.footer-qr {
    text-align: center;
}

.footer-qr img {
    width: 72px;
    height: 72px;
    margin-bottom: 8px;
    background-color: #ffffff;
    border-radius: 4px;
}

.footer-qr p {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
}

.footer-bottom {
    border-top: 1px solid #051233;
    padding-top: 20px;
    text-align: center;
}

.footer-bottom p {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.65);
    letter-spacing: 1px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .container {
        padding: 0 40px;
    }
    
    .nav-wrapper {
        padding: 0 20px;
    }
    
    .banner-text h1 {
        font-size: 42px;
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 40px;
    }
    
    .products-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .factories-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .banner-content {
        flex-direction: column;
        text-align: center;
    }
    
    .banner-text h1 {
        font-size: 32px;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .products-layout {
        flex-direction: column;
    }
    
    .category-section {
        width: 100%;
    }
    
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .case-card {
        flex-direction: column;
    }
    
    .case-image {
        width: 100%;
        height: 250px;
    }
    
    .factories-grid {
        grid-template-columns: 1fr;
    }
    
    .tags-wrapper {
        flex-wrap: wrap;
        justify-content: center;
    }
}
